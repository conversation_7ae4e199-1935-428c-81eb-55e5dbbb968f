"use client";

import { useState, useTransition } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { PlusCircle, Edit, Trash2, Loader2, RefreshCcw } from 'lucide-react';
import type { FAQ } from '@/lib/types';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { useToast } from '@/hooks/use-toast';
import { addFaq } from '@/ai/flows/add-faq';
import { updateFaqAction, deleteFaqAction, regenerateEmbeddingAction } from '@/app/admin/actions';
import { Card } from './ui/card';

const faqSchema = z.object({
  question: z.string().min(10, 'Question must be at least 10 characters long.'),
  answer: z.string().min(10, 'Answer must be at least 10 characters long.'),
});

type FaqFormValues = z.infer<typeof faqSchema>;

interface FaqManagerProps {
    initialFaqs: FAQ[];
}

export default function FaqManager({ initialFaqs }: FaqManagerProps) {
  const [faqs, setFaqs] = useState<FAQ[]>(initialFaqs);
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [editingFaq, setEditingFaq] = useState<FAQ | null>(null);
  const [isPending, startTransition] = useTransition();
  const [isRegenerating, setIsRegenerating] = useState<string | null>(null);
  const { toast } = useToast();

  const form = useForm<FaqFormValues>({
    resolver: zodResolver(faqSchema),
    defaultValues: {
      question: '',
      answer: '',
    },
  });

  const isSubmitting = form.formState.isSubmitting || isPending;

  const handleOpenForm = (faq: FAQ | null = null) => {
    setEditingFaq(faq);
    if (faq) {
      form.reset({ question: faq.question, answer: faq.answer });
    } else {
      form.reset({ question: '', answer: '' });
    }
    setIsFormOpen(true);
  };
  
  const handleCloseForm = () => {
    setIsFormOpen(false);
    setEditingFaq(null);
    form.reset();
  }

  const onSubmit = async (data: FaqFormValues) => {
    startTransition(async () => {
        if (editingFaq) {
          // Edit logic
          const updatedFaq = { ...editingFaq, ...data };
          const result = await updateFaqAction(updatedFaq);
          if (result.success) {
            setFaqs(faqs.map(faq => faq.id === editingFaq.id ? updatedFaq : faq));
            toast({ title: 'FAQ Updated', description: 'The FAQ has been successfully updated.' });
          } else {
            toast({ variant: 'destructive', title: 'Error', description: result.message || 'Failed to update FAQ.' });
          }
        } else {
          // Add logic
          const result = await addFaq(data);
          if (result.success && result.id) {
            const newFaq: FAQ = { id: result.id, ...data };
            setFaqs([newFaq, ...faqs]);
            toast({ title: 'FAQ Added', description: 'The new FAQ has been successfully added.' });
          } else {
            toast({ variant: "destructive", title: 'Error', description: result.message || 'Failed to add FAQ.' });
          }
        }
        handleCloseForm();
    });
  };

  const handleDelete = (id: string) => {
    startTransition(async () => {
        const result = await deleteFaqAction(id);
        if (result.success) {
          setFaqs(faqs.filter(faq => faq.id !== id));
          toast({ title: 'FAQ Deleted', description: 'The FAQ has been deleted.' });
        } else {
          toast({ variant: 'destructive', title: 'Error', description: result.message || 'Failed to delete FAQ.' });
        }
    });
  };
  
  const handleRegenerate = (faq: FAQ) => {
    setIsRegenerating(faq.id);
    startTransition(async () => {
        const result = await regenerateEmbeddingAction(faq.id, faq.question);
        if (result.success) {
          toast({ title: 'Embedding Regenerated', description: 'The search index has been updated.' });
        } else {
          toast({ variant: 'destructive', title: 'Error', description: result.message || 'Failed to regenerate embedding.' });
        }
        setIsRegenerating(null);
    });
  };


  return (
    <>
      <div className="flex justify-end mb-4">
        <Button onClick={() => handleOpenForm()} disabled={isPending || !!isRegenerating}>
          <PlusCircle className="mr-2 h-4 w-4" /> Add New FAQ
        </Button>
      </div>
      <Card>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Question</TableHead>
              <TableHead>Answer</TableHead>
              <TableHead className="w-[160px] text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {faqs.map(faq => (
              <TableRow key={faq.id}>
                <TableCell className="font-medium max-w-sm">{faq.question}</TableCell>
                <TableCell className="text-muted-foreground max-w-md">{faq.answer}</TableCell>
                <TableCell className="text-right space-x-1">
                   <Button variant="ghost" size="icon" onClick={() => handleRegenerate(faq)} disabled={isPending || isRegenerating === faq.id} title="Regenerate Embedding">
                    {isRegenerating === faq.id ? <Loader2 className="h-4 w-4 animate-spin" /> : <RefreshCcw className="h-4 w-4" />}
                    <span className="sr-only">Regenerate Embedding</span>
                  </Button>
                  <Button variant="ghost" size="icon" onClick={() => handleOpenForm(faq)} disabled={isPending || !!isRegenerating}>
                    <Edit className="h-4 w-4" />
                    <span className="sr-only">Edit</span>
                  </Button>
                  <AlertDialog>
                    <AlertDialogTrigger asChild>
                      <Button variant="ghost" size="icon" disabled={isPending || !!isRegenerating}>
                        <Trash2 className="h-4 w-4 text-destructive" />
                         <span className="sr-only">Delete</span>
                      </Button>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                      <AlertDialogHeader>
                        <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                        <AlertDialogDescription>
                          This action cannot be undone. This will permanently delete this FAQ from the database.
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction onClick={() => handleDelete(faq.id)} className="bg-destructive hover:bg-destructive/90">Delete</AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                </TableCell>
              </TableRow>
            ))}
             {faqs.length === 0 && (
                <TableRow>
                    <TableCell colSpan={3} className="text-center h-24 text-muted-foreground">
                        No FAQs found. Add one to get started.
                    </TableCell>
                </TableRow>
            )}
          </TableBody>
        </Table>
      </Card>

      <Dialog open={isFormOpen} onOpenChange={(open) => !isSubmitting && setIsFormOpen(open)}>
        <DialogContent className="sm:max-w-[600px]" onPointerDownOutside={e => isSubmitting && e.preventDefault()} onEscapeKeyDown={e => isSubmitting && e.preventDefault()}>
          <DialogHeader>
            <DialogTitle>{editingFaq ? 'Edit FAQ' : 'Add New FAQ'}</DialogTitle>
            <DialogDescription>
              {editingFaq ? 'Update the details of the FAQ.' : 'Add a new question and answer to the database.'}
            </DialogDescription>
          </DialogHeader>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4 py-4">
              <FormField
                control={form.control}
                name="question"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Question</FormLabel>
                    <FormControl>
                      <Input placeholder="e.g., How much does it cost?" {...field} disabled={isSubmitting} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="answer"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Answer</FormLabel>
                    <FormControl>
                      <Textarea placeholder="Provide a clear and concise answer." {...field} rows={5} disabled={isSubmitting} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <DialogFooter>
                <Button type="button" variant="outline" onClick={handleCloseForm} disabled={isSubmitting}>Cancel</Button>
                <Button type="submit" disabled={isSubmitting}>
                  {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  {isSubmitting ? 'Saving...' : 'Save FAQ'}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
    </>
  );
}

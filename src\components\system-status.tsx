import { checkLlmConnection, checkSupabaseConnection, type StatusCheck } from '@/lib/status-check';
import { <PERSON>, CardContent, CardHeader, CardTitle } from './ui/card';
import { Badge } from './ui/badge';
import { cn } from '@/lib/utils';
import { Server, AlertCircle, CheckCircle2 } from 'lucide-react';

function StatusItem({ check }: { check: StatusCheck }) {
    const isOk = check.status === 'ok';
    const Icon = isOk ? CheckCircle2 : AlertCircle;

    return (
        <div className="flex items-center justify-between space-x-4 p-2 rounded-lg transition-colors hover:bg-muted/50">
            <div className="flex items-center space-x-3">
                 <Icon className={cn("h-5 w-5", isOk ? "text-green-500" : "text-red-500")} />
                <p className="text-sm font-medium leading-none">{check.name}</p>
            </div>
            <div className='flex items-center gap-2'>
                <p className="text-sm text-muted-foreground hidden sm:block truncate max-w-xs" title={check.details}>{check.details}</p>
                <Badge variant={isOk ? 'secondary' : 'destructive'} className={cn(isOk && "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200")}>
                    {check.status.toUpperCase()}
                </Badge>
            </div>
        </div>
    );
}

export default async function SystemStatus() {
    // Run checks in parallel
    const [supabaseStatus, llmStatus] = await Promise.all([
        checkSupabaseConnection(),
        checkLlmConnection(),
    ]);

    const allChecks = [supabaseStatus, llmStatus];
    const allOk = allChecks.every(check => check.status === 'ok');

    return (
        <Card className="w-full max-w-2xl shadow-lg mb-6">
            <CardHeader>
                <CardTitle className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                        <Server className="h-5 w-5 text-primary" />
                        <span>System Status</span>
                    </div>
                    <Badge variant={allOk ? 'secondary' : 'destructive'} className={cn(allOk && 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200')}>
                        {allOk ? 'All Systems Operational' : 'Action Required'}
                    </Badge>
                </CardTitle>
            </CardHeader>
            <CardContent className="space-y-1 p-2 pt-0">
                {allChecks.map(check => (
                    <StatusItem key={check.name} check={check} />
                ))}
            </CardContent>
        </Card>
    );
}

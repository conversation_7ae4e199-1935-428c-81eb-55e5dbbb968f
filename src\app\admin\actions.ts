'use server';

import { revalidatePath } from 'next/cache';
import { supabase } from '@/lib/supabase';
import { ai } from '@/ai/genkit';
import type { FAQ } from '@/lib/types';

export async function regenerateEmbeddingAction(id: string, question: string) {
  try {
    console.log(`[regenerateEmbeddingAction] Generating embedding for FAQ ID: ${id}, Question: "${question}"`);
    if (!question) {
        throw new Error("Question text is empty, cannot generate embedding.");
    }
    const embedding = await ai.embed({
      embedder: 'googleai/text-embedding-004',
      content: question,
    });

    if (!embedding || !Array.isArray(embedding)) {
        console.error("Embedding generation failed or returned an invalid format.", embedding);
        throw new Error("Failed to generate embedding for the question. Please check your LLM provider connection and API key.");
    }

    console.log(`[regenerateEmbeddingAction] Embedding generated. Length: ${embedding.length}. Updating Supabase.`);
    
    const { error } = await supabase
      .from('faqs')
      .update({ embedding: embedding as any })
      .eq('id', id);

    if (error) throw new Error(error.message);

    console.log(`[regenerateEmbeddingAction] Successfully updated embedding for FAQ ID: ${id}`);
    revalidatePath('/admin');
    return { success: true };

  } catch (error) {
    console.error('Error regenerating embedding:', error);
    const message = error instanceof Error ? error.message : 'An unknown error occurred';
    return { success: false, message };
  }
}

export async function updateFaqAction(faq: Pick<FAQ, 'id' | 'question' | 'answer'>) {
  try {
    console.log(`[updateFaqAction] Updating FAQ ID: ${faq.id}. Question: "${faq.question}"`);
    if (!faq.question) {
        throw new Error("Question text is empty, cannot generate embedding.");
    }
    const embedding = await ai.embed({
      embedder: 'googleai/text-embedding-004',
      content: faq.question,
    });
    
    if (!embedding || !Array.isArray(embedding)) {
        console.error("Embedding generation failed or returned an invalid format for update.", embedding);
        throw new Error("Failed to generate embedding for the updated question. Please check your LLM provider connection and API key.");
    }

    console.log(`[updateFaqAction] Embedding generated. Length: ${embedding.length}. Updating Supabase.`);
    
    const { error } = await supabase
      .from('faqs')
      .update({ question: faq.question, answer: faq.answer, embedding: embedding as any })
      .eq('id', faq.id);

    if (error) throw new Error(error.message);

    console.log(`[updateFaqAction] Successfully updated FAQ ID: ${faq.id}`);
    revalidatePath('/admin');
    return { success: true };
  } catch (error) {
    console.error('Error updating FAQ:', error);
    const message = error instanceof Error ? error.message : 'An unknown error occurred';
    return { success: false, message };
  }
}

export async function deleteFaqAction(id: string) {
  try {
    console.log(`[deleteFaqAction] Deleting FAQ with ID: ${id}`);
    const { error } = await supabase.from('faqs').delete().eq('id', id);
    if (error) throw new Error(error.message);

    revalidatePath('/admin');
    return { success: true };
  } catch (error) {
    console.error('Error deleting FAQ:', error);
    const message = error instanceof Error ? error.message : 'An unknown error occurred';
    return { success: false, message };
  }
}

'use server';

/**
 * @fileOverview An AI agent that answers questions using semantic similarity search against a database of FAQs.
 *
 * - answerQuestion - A function that handles the question answering process.
 * - AnswerQuestionInput - The input type for the answerQuestion function.
 * - AnswerQuestionOutput - The return type for the answerQuestion function.
 */

import { ai } from '@/ai/genkit';
import { supabase } from '@/lib/supabase';
import { z } from 'genkit';

const AnswerQuestionInputSchema = z.object({
  question: z.string().describe('The question to be answered.'),
});
export type AnswerQuestionInput = z.infer<typeof AnswerQuestionInputSchema>;

const AnswerQuestionOutputSchema = z.object({
  answer: z.string().describe('The answer to the question.'),
});
export type AnswerQuestionOutput = z.infer<typeof AnswerQuestionOutputSchema>;

export async function answerQuestion(input: AnswerQuestionInput): Promise<AnswerQuestionOutput> {
  return answerQuestionFlow(input);
}

// Define a tool to retrieve relevant FAQs from the database based on semantic similarity.
const getSimilarFAQs = ai.defineTool({
  name: 'getSimilarFAQs',
  description: 'Retrieves similar FAQs from the database based on the given question.',
  inputSchema: z.object({
    question: z.string().describe('The question to find similar FAQs for.'),
  }),
  outputSchema: z.array(z.object({
    question: z.string(),
    answer: z.string(),
    similarity: z.number(),
  })).describe('An array of similar FAQs with their similarity scores.'),
}, async ({ question }) => {
  try {
    console.log(`[getSimilarFAQs] Generating embedding for question: "${question}"`);
    const embedding = await ai.embed({
      embedder: 'googleai/text-embedding-004',
      content: question,
    });
    
    // This threshold can be adjusted.
    const confidenceThreshold = 0.5;
    
    console.log('[getSimilarFAQs] Searching for similar FAQs in Supabase.');
    const { data: faqs, error } = await supabase.rpc('match_faqs', {
      query_embedding: embedding as any,
      match_threshold: confidenceThreshold,
      match_count: 5,
    });

    if (error) {
      console.error('[getSimilarFAQs] Supabase RPC error:', error);
      return [];
    }

    if (!faqs || faqs.length === 0) {
      console.log('[getSimilarFAQs] No similar FAQs found.');
      return [];
    }

    console.log(`[getSimilarFAQs] Found ${faqs.length} similar FAQs.`);

    return faqs.map(faq => ({
      question: faq.question,
      answer: faq.answer,
      similarity: faq.similarity,
    }));
  } catch(e) {
    console.error("[getSimilarFAQs] Error in tool:", e);
    return [];
  }
});

const answerQuestionPrompt = ai.definePrompt({
  name: 'answerQuestionPrompt',
  input: { schema: AnswerQuestionInputSchema },
  output: { schema: AnswerQuestionOutputSchema },
  tools: [getSimilarFAQs],
  prompt: `You are a helpful and friendly customer support chatbot for a company called LinguaServe.
Your goal is to answer user questions accurately based on the information provided in the company's Frequently Asked Questions (FAQs).
You can communicate in both English and Nepali. Respond in the language of the user's question.

User's question: {{{question}}}

INSTRUCTIONS:
1.  First, use the 'getSimilarFAQs' tool to search the FAQ database for relevant information.
2.  Review the search results provided by the tool.
3.  If you find one or more relevant FAQs with a high similarity score, use their content to construct a comprehensive answer. Synthesize the information from multiple FAQs if necessary.
4.  If the tool returns no relevant FAQs, or if their similarity score is low, you MUST state that you could not find an answer in the database. DO NOT try to answer from your own general knowledge. Politely inform the user that the information is not available.
5.  Keep your answers concise and to the point.
6.  Always be polite and professional.

Answer:
  `,
});

const answerQuestionFlow = ai.defineFlow({
  name: 'answerQuestionFlow',
  inputSchema: AnswerQuestionInputSchema,
  outputSchema: AnswerQuestionOutputSchema,
}, async (input) => {
  const { output } = await answerQuestionPrompt(input);
  if (!output) {
    return { answer: "I'm sorry, I encountered an issue and couldn't generate a response. Please try again." };
  }
  return output;
});

'use server';

/**
 * @fileOverview Adds a new FAQ entry to the database, generating embeddings for semantic search.
 *
 * - addFaq - A function that handles adding a new FAQ entry.
 * - AddFaqInput - The input type for the addFaq function.
 * - AddFaqOutput - The return type for the addFaq function.
 */

import {ai} from '@/ai/genkit';
import { supabase } from '@/lib/supabase';
import {z} from 'genkit';

const AddFaqInputSchema = z.object({
  question: z.string().describe('The question for the FAQ entry.'),
  answer: z.string().describe('The answer to the question.'),
});
export type AddFaqInput = z.infer<typeof AddFaqInputSchema>;

const AddFaqOutputSchema = z.object({
  success: z.boolean().describe('Indicates if the FAQ entry was successfully added.'),
  message: z.string().optional().describe('Optional message providing more details about the operation.'),
  id: z.string().optional().describe('The ID of the newly created FAQ entry.'),
});
export type AddFaqOutput = z.infer<typeof AddFaqOutputSchema>;

export async function addFaq(input: AddFaqInput): Promise<AddFaqOutput> {
  return addFaqFlow(input);
}

const addFaqFlow = ai.defineFlow(
  {
    name: 'addFaqFlow',
    inputSchema: AddFaqInputSchema,
    outputSchema: AddFaqOutputSchema,
  },
  async ({ question, answer }) => {
    try {
      console.log(`[addFaqFlow] Generating embedding for question: "${question}"`);
      if (!question) {
        throw new Error("Question text is empty, cannot generate embedding.");
      }
      const embedding = await ai.embed({
        embedder: 'googleai/text-embedding-004',
        content: question,
      });

      if (!embedding || !Array.isArray(embedding)) {
        console.error("Embedding generation failed or returned an invalid format.", embedding);
        throw new Error("Failed to generate embedding for the new question. Please check your LLM provider connection and API key.");
      }

      console.log(`[addFaqFlow] Embedding generated. Length: ${embedding.length}. Type: ${typeof embedding}. Sample: ${embedding.slice(0, 5)}. Inserting into Supabase.`);
      
      const { data, error } = await supabase
        .from('faqs')
        .insert([{ question, answer, embedding }])
        .select('id')
        .single();

      if (error) {
        console.error('Supabase error adding FAQ:', error);
        throw new Error(error.message);
      }
      
      console.log(`[addFaqFlow] Successfully inserted FAQ with ID: ${data.id}`);
      return {
        success: true,
        message: 'FAQ entry added successfully.',
        id: data.id,
      };
    } catch (error) {
      console.error('Error in addFaqFlow:', error);
      const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred.';
      return {
        success: false,
        message: `Failed to add FAQ: ${errorMessage}`,
      };
    }
  }
);

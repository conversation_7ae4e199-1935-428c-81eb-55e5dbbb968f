import FaqManager from "@/components/faq-manager";
import { supabase } from "@/lib/supabase";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Terminal } from "lucide-react";

export default async function AdminPage() {
    const { data: faqs, error } = await supabase
        .from('faqs')
        .select('id, question, answer')
        .order('question', { ascending: true });

    if (error) {
        return (
            <div className="container mx-auto py-8 px-4">
                 <Alert variant="destructive">
                    <Terminal className="h-4 w-4" />
                    <AlertTitle>Error loading FAQs</AlertTitle>
                    <AlertDescription>
                        Could not fetch data from the database. Please check your Supabase connection and configuration.
                        <p className="font-mono text-xs mt-2">{error.message}</p>
                    </AlertDescription>
                </Alert>
            </div>
        )
    }
    
    return (
        <div className="container mx-auto py-8 px-4">
            <h1 className="text-3xl font-bold font-headline mb-2 text-foreground">FAQ Management</h1>
            <p className="text-muted-foreground mb-6">Create, edit, and delete Frequently Asked Questions for the chatbot.</p>
            <FaqManager initialFaqs={faqs || []} />
        </div>
    )
}

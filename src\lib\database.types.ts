export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      faqs: {
        Row: {
          id: string
          question: string
          answer: string
          embedding: number[] | null
        }
        Insert: {
          id?: string
          question: string
          answer: string
          embedding: number[] | null
        }
        Update: {
          id?: string
          question?: string
          answer?: string
          embedding?: number[] | null
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      match_faqs: {
        Args: {
          query_embedding: number[]
          match_threshold: number
          match_count: number
        }
        Returns: {
          id: string
          question: string
          answer: string
          similarity: number
        }[]
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

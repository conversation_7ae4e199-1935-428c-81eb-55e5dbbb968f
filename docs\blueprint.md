# **App Name**: LinguaServe

## Core Features:

- Chat Interface: Chat interface: Provides a user-friendly interface for inputting questions and displaying bot responses.
- Semantic Search: AI-Powered Semantic Search: Uses pgvector and cosine similarity to find the best match to a user's question from the FAQ database.
- LLM Response: LLM Response Generation: Uses a Large Language Model to answer the question. A tool is used to determine when to incorporate results from the database in its response.
- Multilingual Support: Multilingual Support: Supports Nepali-English for a localized chat experience.

## Style Guidelines:

- Primary color: Dark slate blue (#30475E) to evoke trust and intelligence.
- Background color: Very light grayish blue (#E8EAEB) to provide a clean and professional backdrop.
- Accent color: Pale cyan (#82A3A1) for interactive elements and highlights to guide the user.
- Body and headline font: 'Inter' (sans-serif) for a clean, modern, objective look that ensures readability.
-- Enable the pgvector extension to work with embedding vectors
create extension if not exists vector;

-- Create a table to store your FAQs
create table
  faqs (
    id uuid primary key default gen_random_uuid (),
    question text not null,
    answer text not null,
    embedding vector(768) -- Corresponds to the embedding dimension of text-embedding-004
  );

-- Create a function to search for matching FAQs
create function match_faqs (
  query_embedding vector(768),
  match_threshold float,
  match_count int
) returns table (
  id uuid,
  question text,
  answer text,
  similarity float
) language plpgsql as $$
begin
  return query
  select
    faqs.id,
    faqs.question,
    faqs.answer,
    1 - (faqs.embedding <=> query_embedding) as similarity
  from faqs
  where 1 - (faqs.embedding <=> query_embedding) > match_threshold
  order by similarity desc
  limit match_count;
end;
$$;

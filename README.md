# LinguaServe - Multilingual AI Chatbot

This is a full-stack Next.js 14 application that provides a multilingual (Nepali-English) chatbot powered by AI. It uses Supabase with the `pgvector` extension for semantic search on a database of Frequently Asked Questions (FAQs).

## ✨ Features

- **Chat Interface**: A clean, user-friendly chat interface for interacting with the bot.
- **AI-Powered Semantic Search**: Uses vector embeddings and cosine similarity to find the most relevant FAQ, even with informal or multilingual queries.
- **LLM-Generated Responses**: Leverages Large Language Models (LLMs) to provide natural and context-aware answers.
- **FAQ Management**: An admin dashboard to easily add, edit, and delete FAQs.
- **Integrations**: Designed to be easily integrated with automation platforms like n8n or Make.com.
- **Modular LLM Support**: Built with Genkit to easily switch between different LLM providers (Google Gemini, OpenAI, etc.).

## 🔧 Tech Stack

- **Framework**: Next.js 14 (App Router)
- **Language**: TypeScript
- **UI**: ShadCN UI
- **Database**: Supabase (PostgreSQL with `pgvector`)
- **AI/LLM Orchestration**: Genkit

## 🚀 Getting Started

### 1. Prerequisites

- [Node.js](https://nodejs.org/en/) (v18 or newer)
- A package manager like `npm`, `yarn`, or `pnpm`.
- A [Supabase](https://supabase.com/) account
- API keys for your chosen LLM provider (e.g., Google AI Studio for Gemini)

### 2. Clone the Repository

```bash
git clone <repository-url>
cd <repository-name>
```

### 3. Install Dependencies

```bash
npm install
```

### 4. Set up Supabase

1.  **Create a new project** on [Supabase](https://supabase.com/).
2.  Go to the **SQL Editor** in your Supabase project dashboard.
3.  Copy the contents of `supabase.schema.sql` from this repository and run it. This will:
    - Enable the `vector` extension.
    - Create the `faqs` table with an `embedding` column.
    - Create a function `match_faqs` for semantic search.

### 5. Configure Environment Variables

1.  Create a `.env.local` file in the root of your project by copying the example file:
    ```bash
    cp .env.local.example .env.local
    ```
2.  **Fill in the Supabase details**:
    - Go to **Project Settings > API** in your Supabase dashboard.
    - Find your **Project URL** and `anon` **public key** and add them to `.env.local`.
    - You will also need the **`service_role` key**. Be very careful with this key, as it bypasses Row Level Security.

3.  **Fill in your LLM API Key**:
    - Choose your LLM provider (default is `gemini`).
    - Get your API key from the provider (e.g., [Google AI Studio](https://ai.google.dev/)).
    - Add the key to the corresponding variable in `.env.local`.

Your `.env.local` should look like this:

```
# Supabase
NEXT_PUBLIC_SUPABASE_URL=https://<your-project-ref>.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=<your-anon-key>
SUPABASE_SERVICE_ROLE_KEY=<your-service-role-key>

# Genkit/LLM Config
LLM_PROVIDER=gemini
GEMINI_API_KEY=<your-gemini-api-key>

# Optional keys for other providers
OPENAI_API_KEY=
OPENROUTER_API_KEY=
ANTHROPIC_API_KEY=

# Vector Search Threshold
CONFIDENCE_THRESHOLD=0.8
```

### 6. Run the Development Server

```bash
npm run dev
```

The application will be available at `http://localhost:9002`.

- **Chat Interface**: `http://localhost:9002/`
- **Admin Dashboard**: `http://localhost:9002/admin`

## 🔌 Using with n8n or Make.com

The core logic is handled by a Genkit flow. To integrate with an external service like n8n, you would need to create a dedicated API route that calls the Genkit flow.

**Example API Route (`src/app/api/ask/route.ts`)**:

```typescript
// src/app/api/ask/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { answerQuestion } from '@/ai/flows/answer-question';

export async function POST(req: NextRequest) {
  try {
    const { question } = await req.json();

    if (!question || typeof question !== 'string') {
      return NextResponse.json({ error: 'Question is required' }, { status: 400 });
    }

    const result = await answerQuestion({ question });
    return NextResponse.json(result);

  } catch (error) {
    console.error('API Error:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}
```
You can now send `POST` requests to `http://<your-app-url>/api/ask` with a JSON body like `{"question": "How much does it cost?"}`.

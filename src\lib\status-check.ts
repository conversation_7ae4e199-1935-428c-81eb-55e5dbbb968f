'use server';
import { supabase } from './supabase';
import { ai } from '@/ai/genkit';

type Status = 'ok' | 'error' | 'pending';
export interface StatusCheck {
    name: string;
    status: Status;
    details: string;
}

export async function checkSupabaseConnection(): Promise<StatusCheck> {
    try {
        const { error } = await supabase.from('faqs').select('id', { count: 'exact', head: true });
        if (error) {
            if (error.code === '42P01') {
                 return { name: 'Supabase: Table Schema', status: 'error', details: '`faqs` table not found. Did you run the schema setup SQL?' };
            }
            throw error;
        }
        return { name: 'Supabase: DB & Table', status: 'ok', details: 'Connection and schema OK.' };
    } catch (e: any) {
        return { name: 'Supabase: DB & Table', status: 'error', details: e.message || 'Failed to connect.' };
    }
}

export async function checkLlmConnection(): Promise<StatusCheck> {
    try {
        // Check both text generation and embedding models
        await ai.generate({ prompt: 'Health check', config: { temperature: 0 } });
        await ai.embed({ embedder: 'googleai/text-embedding-004', content: 'Health check' });

        return { name: 'LLM Connection', status: 'ok', details: 'Generation and embedding models are operational.' };
    } catch (e: any) {
        return { name: 'LLM Connection', status: 'error', details: e.message || 'Failed to connect. Check API key and model permissions.' };
    }
}
